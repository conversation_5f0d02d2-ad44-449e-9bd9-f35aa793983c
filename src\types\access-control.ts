/**
 * Access control types for users
 */

export type UserRole = 'admin' | 'contractor' | 'viewer';
export type AdminAccessMode = 'state' | 'project';
export type StateCode =
  | 'JH'
  | 'KD'
  | 'KT'
  | 'ML'
  | 'NS'
  | 'PH'
  | 'PN'
  | 'PK'
  | 'PL'
  | 'SB'
  | 'SW'
  | 'SL'
  | 'TR'
  | 'WP'
  | 'LBN'
  | 'PW'
  | 'OTH';

export interface User {
  id: string;
  name: string;
  email: string;
  user_role: UserRole;
  admin_access_mode?: AdminAccessMode | null; // Required for admin users
  monitoring_state?: StateCode | null; // Required for admin with state access mode
  contractor_id?: string | null;
  onboarding_completed?: boolean; // Required for project access
}

export interface Project {
  id: string;
  name: string;
  code?: string | null;
  state?: StateCode | null;
  contractor_id?: string | null;
  location?: string | null;
  status?: string | null;
  start_date?: string | null;
  end_date?: string | null;
  // ... other project fields
}

/**
 * Access control utility functions
 */
export class AccessControl {
  /**
   * Check if a user can view a specific project
   */
  static canUserViewProject(user: User, project: Project): boolean {
    // SECURITY: Users must complete onboarding before accessing any projects
    if (!user.onboarding_completed) {
      return false;
    }

    if (user.user_role === 'admin') {
      if (user.admin_access_mode === 'project') {
        // Project-based admin can see all projects regardless of state
        return true;
      }

      if (user.admin_access_mode === 'state' && user.monitoring_state) {
        // State-based admin can only see projects in their monitoring state
        return user.monitoring_state === project.state;
      }

      // Admin without proper access mode configuration has no access
      return false;
    }

    if (user.user_role === 'contractor' && user.contractor_id) {
      // Contractors can only see their own projects
      return user.contractor_id === project.contractor_id;
    }

    // Viewer users or users without proper configuration have no access
    return false;
  }

  /**
   * Filter projects based on user's access level
   */
  static filterProjectsForUser(user: User, projects: Project[]): Project[] {
    // SECURITY: Users must complete onboarding before accessing any projects
    if (!user.onboarding_completed) {
      return [];
    }

    if (user.user_role === 'admin') {
      if (user.admin_access_mode === 'project') {
        // Project-based admin can see all projects
        return projects;
      }

      if (user.admin_access_mode === 'state' && user.monitoring_state) {
        // State-based admin can only see projects in their monitoring state
        return projects.filter(
          (project) => project.state === user.monitoring_state,
        );
      }

      // Admin without proper access mode configuration has no access
      return [];
    }

    if (user.user_role === 'contractor' && user.contractor_id) {
      // Contractors can only see their own projects
      return projects.filter(
        (project) => project.contractor_id === user.contractor_id,
      );
    }

    // Viewer users or users without proper configuration return empty array
    return [];
  }

  /**
   * Get SQL WHERE clause for project filtering based on user role
   */
  static getProjectFilterSQL(user: User): {
    where: string;
    params: Record<string, unknown>;
  } {
    if (user.user_role === 'admin') {
      if (user.admin_access_mode === 'project') {
        // Project-based admin can see all projects - no filter needed
        return { where: '1=1', params: {} };
      }

      if (user.admin_access_mode === 'state') {
        // State-based admin can only see projects in their monitoring state
        return {
          where: 'projects.state = $monitoring_state',
          params: { monitoring_state: user.monitoring_state },
        };
      }
    }

    if (user.user_role === 'contractor') {
      // Contractors can only see their own projects
      return {
        where: 'projects.contractor_id = $contractor_id',
        params: { contractor_id: user.contractor_id },
      };
    }

    // Default: no access
    return { where: '1=0', params: {} };
  }
}

// ================================
// SUBSCRIPTION-BASED ACCESS CONTROL
// Enhanced access control with payment gateway integration
// ================================

/**
 * Access states for project access control with subscription status
 */
export enum AccessState {
  FULL_ACCESS = 'full_access', // Active subscription, full project access
  PAYMENT_REQUIRED = 'payment_required', // Subscription pending payment, no access
  GRACE_PERIOD = 'grace_period', // Failed payment, grace period active
  SUSPENDED = 'suspended', // Grace period expired, access suspended
  ACCESS_DENIED = 'access_denied', // Cancelled or other denial reasons
}

/**
 * Detailed project access information including subscription status
 */
export interface ProjectAccessInfo {
  // Basic access information
  projectId: string;
  projectName: string;
  contractorId: string;
  hasAccess: boolean;
  accessState: AccessState;

  // Subscription details
  subscriptionId?: string;
  subscriptionStatus?:
    | 'active'
    | 'pending_payment'
    | 'grace_period'
    | 'cancelled'
    | 'suspended';

  // Payment information
  nextBillingDate?: string;
  lastPaymentDate?: string;
  lastPaymentStatus?: 'pending' | 'paid' | 'failed' | 'cancelled' | 'refunded';

  // Grace period information
  gracePeriodEnds?: string;
  daysRemainingInGracePeriod?: number;

  // Action information
  actionRequired: boolean;
  actionType?:
    | 'payment_required'
    | 'payment_overdue'
    | 'subscription_expired'
    | 'none';

  // User messaging
  accessMessage: string;
  warningMessage?: string;

  // UI control flags
  showPaymentBanner: boolean;
  showGracePeriodWarning: boolean;
  allowReadOnlyAccess: boolean;
}

/**
 * Subscription access rules configuration
 */
export type SubscriptionAccessRules = {
  // Status-based access rules
  accessRules: {
    [K in
      | 'active'
      | 'pending_payment'
      | 'grace_period'
      | 'cancelled'
      | 'suspended']: {
      allowAccess: boolean;
      allowReadOnly?: boolean;
      showWarning: boolean;
      warningLevel: 'info' | 'warning' | 'error';
      actionRequired: boolean;
      accessMessage: string;
    };
  };

  // Grace period configuration
  gracePeriod: {
    durationDays: number;
    warningThresholdDays: number; // Show urgent warning when this many days left
    allowFullAccess: boolean;
  };

  // UI behavior configuration
  uiBehavior: {
    redirectToPayment: boolean;
    showPaymentBanner: boolean;
    allowProjectNavigation: boolean;
    showGracePeriodCountdown: boolean;
  };
};

/**
 * Default subscription access rules
 */
export const DEFAULT_SUBSCRIPTION_ACCESS_RULES: SubscriptionAccessRules = {
  accessRules: {
    active: {
      allowAccess: true,
      showWarning: false,
      warningLevel: 'info',
      actionRequired: false,
      accessMessage: 'Full project access',
    },
    pending_payment: {
      allowAccess: false,
      showWarning: true,
      warningLevel: 'warning',
      actionRequired: true,
      accessMessage: 'Payment required to access project',
    },
    grace_period: {
      allowAccess: true,
      showWarning: true,
      warningLevel: 'error',
      actionRequired: true,
      accessMessage: 'Grace period active - payment required soon',
    },
    cancelled: {
      allowAccess: false,
      showWarning: true,
      warningLevel: 'info',
      actionRequired: false,
      accessMessage: 'Subscription cancelled',
    },
    suspended: {
      allowAccess: false,
      showWarning: true,
      warningLevel: 'error',
      actionRequired: true,
      accessMessage: 'Access suspended due to non-payment',
    },
  },
  gracePeriod: {
    durationDays: 7,
    warningThresholdDays: 3,
    allowFullAccess: true,
  },
  uiBehavior: {
    redirectToPayment: true,
    showPaymentBanner: true,
    allowProjectNavigation: false,
    showGracePeriodCountdown: true,
  },
};

/**
 * Enhanced access control class with subscription-based rules
 */
export class SubscriptionAccessControl extends AccessControl {
  /**
   * Check project access based on subscription status
   */
  static checkProjectAccessWithSubscription(
    user: User,
    project: Project,
    subscriptionStatus?:
      | 'active'
      | 'pending_payment'
      | 'grace_period'
      | 'cancelled'
      | 'suspended',
    gracePeriodEnds?: string | null,
  ): ProjectAccessInfo {
    // First check traditional RBAC access
    const hasBasicAccess = this.canUserViewProject(user, project);

    if (!hasBasicAccess) {
      return {
        projectId: project.id,
        projectName: project.name,
        contractorId: user.contractor_id || '',
        hasAccess: false,
        accessState: AccessState.ACCESS_DENIED,
        actionRequired: false,
        accessMessage: 'Access denied - insufficient permissions',
        showPaymentBanner: false,
        showGracePeriodWarning: false,
        allowReadOnlyAccess: false,
      };
    }

    // For non-contractors, use traditional access control
    if (user.user_role !== 'contractor') {
      return {
        projectId: project.id,
        projectName: project.name,
        contractorId: user.contractor_id || '',
        hasAccess: true,
        accessState: AccessState.FULL_ACCESS,
        actionRequired: false,
        accessMessage: 'Full access granted',
        showPaymentBanner: false,
        showGracePeriodWarning: false,
        allowReadOnlyAccess: false,
      };
    }

    // For contractors, check subscription status
    if (!subscriptionStatus) {
      return {
        projectId: project.id,
        projectName: project.name,
        contractorId: user.contractor_id || '',
        hasAccess: false,
        accessState: AccessState.PAYMENT_REQUIRED,
        actionRequired: true,
        actionType: 'payment_required',
        accessMessage: 'Subscription required to access project',
        showPaymentBanner: true,
        showGracePeriodWarning: false,
        allowReadOnlyAccess: false,
      };
    }

    const rules =
      DEFAULT_SUBSCRIPTION_ACCESS_RULES.accessRules[subscriptionStatus];
    let accessState: AccessState;
    let daysRemaining: number | undefined;

    // Determine access state based on subscription status
    switch (subscriptionStatus) {
      case 'active':
        accessState = AccessState.FULL_ACCESS;
        break;
      case 'pending_payment':
        accessState = AccessState.PAYMENT_REQUIRED;
        break;
      case 'grace_period':
        accessState = AccessState.GRACE_PERIOD;
        daysRemaining = gracePeriodEnds
          ? this.calculateDaysRemaining(gracePeriodEnds)
          : undefined;
        break;
      case 'cancelled':
        accessState = AccessState.ACCESS_DENIED;
        break;
      case 'suspended':
        accessState = AccessState.SUSPENDED;
        break;
      default:
        accessState = AccessState.ACCESS_DENIED;
    }

    return {
      projectId: project.id,
      projectName: project.name,
      contractorId: user.contractor_id || '',
      hasAccess: rules.allowAccess,
      accessState,
      subscriptionStatus,
      daysRemainingInGracePeriod: daysRemaining,
      gracePeriodEnds: gracePeriodEnds || undefined,
      actionRequired: rules.actionRequired,
      actionType: this.getActionType(subscriptionStatus),
      accessMessage: rules.accessMessage,
      warningMessage: this.getWarningMessage(subscriptionStatus, daysRemaining),
      showPaymentBanner: subscriptionStatus !== 'active',
      showGracePeriodWarning: subscriptionStatus === 'grace_period',
      allowReadOnlyAccess: rules.allowReadOnly || false,
    };
  }

  /**
   * Calculate days remaining from a future date string
   */
  private static calculateDaysRemaining(endDate: string): number {
    const now = new Date();
    const end = new Date(endDate);
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  /**
   * Get action type based on subscription status
   */
  private static getActionType(
    status: string,
  ): ProjectAccessInfo['actionType'] {
    switch (status) {
      case 'pending_payment':
        return 'payment_required';
      case 'grace_period':
        return 'payment_overdue';
      case 'suspended':
        return 'payment_overdue';
      case 'cancelled':
        return 'subscription_expired';
      default:
        return 'none';
    }
  }

  /**
   * Get warning message based on subscription status and days remaining
   */
  private static getWarningMessage(
    status: string,
    daysRemaining?: number,
  ): string | undefined {
    switch (status) {
      case 'pending_payment':
        return 'Payment is required to access this project';
      case 'grace_period':
        return daysRemaining !== undefined
          ? `Grace period expires in ${daysRemaining} day${daysRemaining !== 1 ? 's' : ''}`
          : 'Grace period active - payment required';
      case 'suspended':
        return 'Access suspended due to non-payment';
      case 'cancelled':
        return 'Subscription has been cancelled';
      default:
        return undefined;
    }
  }
}
