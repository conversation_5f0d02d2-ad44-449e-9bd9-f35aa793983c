#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to verify that the project access control fix is properly implemented
 * This script checks the key files to ensure security measures are in place
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Project Access Control Fix...\n');

// Files to check
const filesToCheck = [
  {
    path: 'src/hooks/use-project-access.ts',
    checks: [
      {
        name: 'Onboarding completion check',
        pattern: /if\s*\(\s*!user\.profile\.onboarding_completed\s*\)/,
        required: true
      },
      {
        name: 'Admin access mode validation',
        pattern: /admin_access_mode\s*===\s*['"]project['"]|admin_access_mode\s*===\s*['"]state['"]/, 
        required: true
      },
      {
        name: 'Accepted invitation check',
        pattern: /\.eq\(\s*['"]status['"],\s*['"]accepted['"]\s*\)/,
        required: true
      },
      {
        name: 'No fallback access (security vulnerability)',
        pattern: /conditions\.push\(\s*['"]id\.neq\.00000000-0000-0000-0000-000000000000['"]\s*\)\s*;?\s*$/m,
        required: false,
        shouldNotExist: true
      }
    ]
  },
  {
    path: 'src/types/access-control.ts',
    checks: [
      {
        name: 'Onboarding completed field in User interface',
        pattern: /onboarding_completed\?\s*:\s*boolean/,
        required: true
      },
      {
        name: 'canUserViewProject onboarding check',
        pattern: /if\s*\(\s*!user\.onboarding_completed\s*\)/,
        required: true
      }
    ]
  }
];

let allChecksPassed = true;
let totalChecks = 0;
let passedChecks = 0;

for (const file of filesToCheck) {
  console.log(`📁 Checking ${file.path}...`);
  
  if (!fs.existsSync(file.path)) {
    console.log(`   ❌ File not found: ${file.path}`);
    allChecksPassed = false;
    continue;
  }
  
  const content = fs.readFileSync(file.path, 'utf8');
  
  for (const check of file.checks) {
    totalChecks++;
    const found = check.pattern.test(content);
    
    if (check.shouldNotExist) {
      // This pattern should NOT exist (security vulnerability)
      if (!found) {
        console.log(`   ✅ ${check.name} - Vulnerability not present`);
        passedChecks++;
      } else {
        console.log(`   ❌ ${check.name} - SECURITY VULNERABILITY DETECTED!`);
        allChecksPassed = false;
      }
    } else if (check.required) {
      // This pattern should exist
      if (found) {
        console.log(`   ✅ ${check.name} - Found`);
        passedChecks++;
      } else {
        console.log(`   ❌ ${check.name} - Missing`);
        allChecksPassed = false;
      }
    }
  }
  
  console.log('');
}

// Summary
console.log('📊 Summary:');
console.log(`   Total checks: ${totalChecks}`);
console.log(`   Passed: ${passedChecks}`);
console.log(`   Failed: ${totalChecks - passedChecks}`);
console.log('');

if (allChecksPassed) {
  console.log('🎉 All security checks passed! The access control fix is properly implemented.');
  console.log('');
  console.log('✅ Key security measures verified:');
  console.log('   • Onboarding completion is required before project access');
  console.log('   • Admin access mode is explicitly validated');
  console.log('   • Only accepted project invitations grant access');
  console.log('   • No fallback logic that grants broad access');
  console.log('');
  console.log('🔒 The vulnerability has been successfully fixed!');
  process.exit(0);
} else {
  console.log('❌ Some security checks failed. Please review the implementation.');
  console.log('');
  console.log('🚨 SECURITY RISK: The access control vulnerability may still exist!');
  console.log('');
  console.log('📋 Next steps:');
  console.log('   1. Review the failed checks above');
  console.log('   2. Ensure all required security measures are implemented');
  console.log('   3. Test with actual user scenarios');
  console.log('   4. Re-run this verification script');
  process.exit(1);
}
