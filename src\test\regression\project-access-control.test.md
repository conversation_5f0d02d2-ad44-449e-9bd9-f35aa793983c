# Project Access Control Regression Test

## Issue Description
Newly registered admin and contractor users can see unrelated projects immediately after registration, even if they:
- Haven't received or accepted a project invitation
- Haven't been assigned to any project

## Root Cause
The `useAccessibleProjects` hook in `src/hooks/use-project-access.ts` had flawed logic:

1. **Admin users**: If `admin_access_mode` was not 'state', they would get ALL projects (fallback logic)
2. **Contractor users**: If they didn't have `contractor_id` set, the conditions array would be empty but still processed
3. **Onboarding check**: No validation that users completed onboarding before accessing projects
4. **Project membership**: No validation that project_users entries had 'accepted' status

## Fix Applied

### 1. Added Onboarding Completion Check
```typescript
// SECURITY: Users who haven't completed onboarding should not see any projects
if (!user.profile.onboarding_completed) {
  return [];
}
```

### 2. Fixed Admin Access Logic
```typescript
// Before (VULNERABLE):
if (user.profile.admin_access_mode === 'state' && user.profile.monitoring_state) {
  conditions.push(`state.eq.${user.profile.monitoring_state}`);
} else {
  // This gave ALL projects to any admin without proper setup!
  conditions.push('id.neq.00000000-0000-0000-0000-000000000000');
}

// After (SECURE):
if (user.profile.admin_access_mode === 'state' && user.profile.monitoring_state) {
  conditions.push(`state.eq.${user.profile.monitoring_state}`);
} else if (user.profile.admin_access_mode === 'project') {
  conditions.push('id.neq.00000000-0000-0000-0000-000000000000');
}
// If admin_access_mode is null/undefined, no access is granted
```

### 3. Added Accepted Invitation Check
```typescript
// Only consider accepted project memberships
const { data: memberProjectIds } = await supabase
  .from('project_users')
  .select('project_id')
  .eq('user_id', user.id)
  .eq('is_active', true)
  .eq('status', 'accepted'); // Only accepted invitations
```

### 4. Updated AccessControl Utility
- Added `onboarding_completed` field to User interface
- Updated `canUserViewProject` and `filterProjectsForUser` methods to check onboarding completion
- Added explicit validation for admin_access_mode and contractor_id

## Test Scenarios

### Scenario 1: Newly Registered Admin (Before Onboarding)
**Setup:**
- User registers as admin
- `onboarding_completed = false`
- `admin_access_mode = null`
- `monitoring_state = null`

**Expected Result:** No projects visible
**Verification:** `useAccessibleProjects()` should return empty array

### Scenario 2: Newly Registered Contractor (Before Onboarding)
**Setup:**
- User registers as contractor
- `onboarding_completed = false`
- `contractor_id = null`

**Expected Result:** No projects visible
**Verification:** `useAccessibleProjects()` should return empty array

### Scenario 3: Admin After Onboarding (State-based)
**Setup:**
- `onboarding_completed = true`
- `admin_access_mode = 'state'`
- `monitoring_state = 'KL'`

**Expected Result:** Only projects in KL state visible
**Verification:** `useAccessibleProjects()` should return projects where `state = 'KL'`

### Scenario 4: Admin After Onboarding (Project-based)
**Setup:**
- `onboarding_completed = true`
- `admin_access_mode = 'project'`
- `monitoring_state = null`

**Expected Result:** All projects visible
**Verification:** `useAccessibleProjects()` should return all active projects

### Scenario 5: Contractor After Onboarding
**Setup:**
- `onboarding_completed = true`
- `contractor_id = 'company-uuid'`

**Expected Result:** Only projects assigned to their company visible
**Verification:** `useAccessibleProjects()` should return projects where `contractor_id = 'company-uuid'`

### Scenario 6: User with Pending Project Invitation
**Setup:**
- User has entry in `project_users` table
- `status = 'invited'` (not 'accepted')
- `is_active = true`

**Expected Result:** Project not visible until invitation is accepted
**Verification:** `useAccessibleProjects()` should not include the project

### Scenario 7: User with Accepted Project Invitation
**Setup:**
- User has entry in `project_users` table
- `status = 'accepted'`
- `is_active = true`
- `onboarding_completed = true`

**Expected Result:** Project visible
**Verification:** `useAccessibleProjects()` should include the project

## Manual Testing Steps

1. **Test New Admin Registration:**
   - Register new admin account
   - Verify no projects appear on dashboard before onboarding
   - Complete onboarding with state-based access
   - Verify only projects in selected state appear

2. **Test New Contractor Registration:**
   - Register new contractor account
   - Verify no projects appear on dashboard before onboarding
   - Complete onboarding by creating/joining company
   - Verify only company's projects appear

3. **Test Project Invitations:**
   - Invite user to project (status = 'invited')
   - Verify project doesn't appear until invitation is accepted
   - Accept invitation (status = 'accepted')
   - Verify project now appears

## Files Modified
- `src/hooks/use-project-access.ts`
- `src/types/access-control.ts`

## Security Impact
- **HIGH**: Prevents unauthorized access to project data
- **CRITICAL**: Ensures proper access control during user onboarding
- **IMPORTANT**: Validates invitation acceptance before granting access

## Regression Prevention
- Always check `onboarding_completed` before granting project access
- Explicitly validate `admin_access_mode` and `contractor_id` values
- Only consider 'accepted' project_users entries
- Never use fallback logic that grants broad access
