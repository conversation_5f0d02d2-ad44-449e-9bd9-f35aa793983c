/**
 * Custom hook for project access control
 * This demonstrates how to implement the JKR state-based access control
 */

import { useUserWithProfile } from '@/hooks/use-auth';
import { supabase } from '@/lib/supabase';
import { AccessControl, Project } from '@/types/access-control';
import { useQuery } from '@tanstack/react-query';

/**
 * Hook to fetch projects based on user's access level
 * Includes projects owned by user's organization AND projects they're members of via invitations
 */
export function useAccessibleProjects() {
  const { data: user } = useUserWithProfile(); // Get current user with profile

  return useQuery({
    queryKey: [
      'accessible-projects',
      user?.id,
      user?.profile?.user_role,
      user?.profile?.monitoring_state,
      user?.profile?.contractor_id,
    ],
    queryFn: async (): Promise<Project[]> => {
      if (!user?.profile) return [];

      // SECURITY: Users who haven't completed onboarding should not see any projects
      if (!user.profile.onboarding_completed) {
        return [];
      }

      // Get project IDs where user is a member with accepted invitations
      const { data: memberProjectIds } = await supabase
        .from('project_users')
        .select('project_id')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .eq('status', 'accepted'); // Only accepted invitations

      const memberProjectIdList =
        memberProjectIds?.map((p) => p.project_id) || [];

      // Build the main query
      let query = supabase
        .from('projects')
        .select(
          `
          id,
          name,
          code,
          state,
          location,
          start_date,
          end_date,
          status,
          contractor_id,
          agency_id
        `,
        )
        .is('deleted_at', null); // Only active projects

      // Build access control conditions
      const conditions: string[] = [];

      // Apply role-based access control
      if (user.profile.user_role === 'admin') {
        // Admin users: only grant access if they have proper configuration
        if (
          user.profile.admin_access_mode === 'state' &&
          user.profile.monitoring_state
        ) {
          // State-based access (equivalent to old JKR_PIC)
          conditions.push(`state.eq.${user.profile.monitoring_state}`);
        } else if (user.profile.admin_access_mode === 'project') {
          // Project-based access (equivalent to old JKR_Admin) gets all projects
          conditions.push('id.neq.00000000-0000-0000-0000-000000000000'); // Always true condition
        }
        // If admin_access_mode is null/undefined, no access is granted
      } else if (
        user.profile.user_role === 'contractor' &&
        user.profile.contractor_id
      ) {
        // Contractor access: only if they have a contractor_id set
        conditions.push(`contractor_id.eq.${user.profile.contractor_id}`);
      }

      // Add projects where user is a member (with accepted invitations)
      if (memberProjectIdList.length > 0) {
        conditions.push(`id.in.(${memberProjectIdList.join(',')})`);
      }

      // Apply OR condition to include both owned and member projects
      if (conditions.length > 0) {
        query = query.or(conditions.join(','));
      } else {
        // Fallback: no access - return empty result
        query = query.eq('id', '00000000-0000-0000-0000-000000000000'); // Never matches
      }

      const { data, error } = await query.order('created_at', {
        ascending: false,
      });

      if (error) {
        throw new Error(`Failed to fetch projects: ${error.message}`);
      }

      return data || [];
    },
    enabled: !!user,
  });
}

/**
 * Hook to check if current user can view a specific project
 */
export function useCanViewProject(projectId: string) {
  const { data: user } = useUserWithProfile();

  return useQuery({
    queryKey: ['can-view-project', user?.id, projectId],
    queryFn: async (): Promise<boolean> => {
      if (!user?.profile || !projectId) return false;

      // SECURITY: Users who haven't completed onboarding should not view any projects
      if (!user.profile.onboarding_completed) {
        return false;
      }

      // Fetch the project
      const { data: project, error } = await supabase
        .from('projects')
        .select(
          'id, name, code, state, location, start_date, end_date, status, contractor_id, agency_id',
        )
        .eq('id', projectId)
        .is('deleted_at', null)
        .single();

      if (error || !project) return false;

      // Check if user is a member of this project with accepted invitation
      const { data: membership } = await supabase
        .from('project_users')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .eq('is_active', true)
        .eq('status', 'accepted')
        .single();

      // Transform user to match AccessControl interface
      const accessControlUser = {
        id: user.id,
        name: user.profile.name || '',
        email: user.email || '',
        user_role: user.profile.user_role,
        admin_access_mode: user.profile.admin_access_mode,
        monitoring_state: user.profile.monitoring_state,
        contractor_id: user.profile.contractor_id,
      };

      // Check access using our access control utility or membership
      const hasRoleBasedAccess = AccessControl.canUserViewProject(accessControlUser, project);
      const hasMembershipAccess = !!membership;

      return hasRoleBasedAccess || hasMembershipAccess;
    },
    enabled: !!user && !!projectId,
  });
}

/**
 * Hook specifically for JKR users to get their monitoring scope
 */
export function useJKRMonitoringScope() {
  const { data: user } = useUserWithProfile();

  const scope = {
    canViewAllStates:
      user?.profile?.user_role === 'admin' &&
      user?.profile?.admin_access_mode === 'project',
    monitoringState:
      user?.profile?.user_role === 'admin' &&
      user?.profile?.admin_access_mode === 'state'
        ? user.profile.monitoring_state
        : null,
    isJKRUser: user?.profile?.user_role === 'admin',
  };

  return scope;
}
